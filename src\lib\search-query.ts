"use server"

import prisma from "./prisma"

const getCourses = (query: string) => {
  return prisma.course.findMany({
    where: {
      OR: [
        { title: { contains: query, mode: "insensitive" } },
        { description: { contains: query, mode: "insensitive" } },
        { seoDescription: { contains: query, mode: "insensitive" } },
      ],
    },
    select: { id: true, title: true, description: true },
    take: 10,
  })
}

const getBlogPosts = (query: string) => {
  return prisma.blogPost.findMany({
    where: {
      OR: [
        { title: { contains: query, mode: "insensitive" } },
        { content: { contains: query, mode: "insensitive" } },
        { seoDescription: { contains: query, mode: "insensitive" } },
      ],
    },
    select: { id: true, title: true, seoDescription: true },
    take: 10,
  })
}

const getArticles = (query: string) => {
  return prisma.article.findMany({
    where: {
      OR: [
        { title: { contains: query, mode: "insensitive" } },
        { article: { contains: query, mode: "insensitive" } },
        { seoDescription: { contains: query, mode: "insensitive" } },
      ],
    },
    select: { id: true, title: true, seoDescription: true },
    take: 10,
  })
}

const getInterviews = (query: string) => {
  return prisma.interview.findMany({
    where: {
      OR: [
        { title: { contains: query, mode: "insensitive" } },
        { description: { contains: query, mode: "insensitive" } },
        { seoDescription: { contains: query, mode: "insensitive" } },
      ],
    },
    select: { id: true, title: true, seoDescription: true },
    take: 10,
  })
}

const getBooks = (query: string) => {
  return prisma.book.findMany({
    where: {
      OR: [
        { title: { contains: query, mode: "insensitive" } },
        { summary: { contains: query, mode: "insensitive" } },
        { seoDescription: { contains: query, mode: "insensitive" } },
      ],
    },
    select: { id: true, title: true, seoDescription: true },
    take: 10,
  })
}

export const getSearchQuery = async (
  query: string,
  tableName?: "course" | "blogPost" | "article" | "interview" | "book"
) => {
  if (!query) return {}

  if (tableName) {
    switch (tableName) {
      case "course":
        return { courses: await getCourses(query) }

      case "blogPost":
        return { blogPosts: await getBlogPosts(query) }

      case "article":
        return { articles: await getArticles(query) }

      case "interview":
        return { interviews: await getInterviews(query) }

      case "book":
        return { books: await getBooks(query) }
    }
  }

  const [courses, blogPosts, articles, interviews, books] = await Promise.all([
    getCourses(query),
    getBlogPosts(query),
    getArticles(query),
    getInterviews(query),
    getBooks(query),
  ])

  return {
    courses,
    blogPosts,
    articles,
    interviews,
    books,
  }
}

export type GetSearchQueryResult = Awaited<ReturnType<typeof getSearchQuery>>
