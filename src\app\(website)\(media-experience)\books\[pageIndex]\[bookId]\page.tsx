import Comments from "@/components/ui/comments/comments";
import PageDivider from "@/components/ui/page-divider";
import BadgePrice from "@/components/ui/badge-price";
import FormatTime from "@/components/ui/format-time";
import Image from "next/image";
import Link from "next/link";

import { CalendarDays, Download } from "lucide-react";
import { getBooks } from "@/utils/get-data-from-db";
import { Button } from "@/components/ui/button";
import { siteName } from "@/utils/siteConfig";
import { notFound } from "next/navigation";
import { Book } from "@prisma/client";
import { Metadata } from "next";

type bookPageProps = {
  params: Promise<{ bookId: string; pageIndex: string }>;
};

const genarateData = async (props: bookPageProps) => {
  const params = await props.params;
  const bookId = params.bookId;
  const pageIndex = Number(params.pageIndex);

  if (!pageIndex) return notFound();

  const { data: response } = await getBooks({ pageIndex });
  const data = response?.find((book) => book.id === bookId);
  const related = response?.filter((book) => book.id !== bookId);

  if (!data) return notFound();

  return { data, related, pageIndex };
};

export async function generateMetadata(props: bookPageProps): Promise<Metadata> {
  const { data, pageIndex } = await genarateData(props);

  return {
    title: data.title,
    description: data.seoDescription,
    keywords: data.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar_AR",
      siteName: siteName,
      images: data.coverImage,
      description: data.seoDescription,
      title: `${data.title} | د. ناهد باشطح`,
      url: `/media-experience/books/${pageIndex}/${data.id}`,
    },
  };
}

// ==================================================================================
// صفحة الكتاب
// ==================================================================================
export default async function BookPage(props: bookPageProps) {
  const { data, related, pageIndex } = await genarateData(props);

  return (
    <PageDivider
      pageContents={<BookContent book={data} />}
      titleMenu="كتب ذات صلة"
      menuItems={related?.map((book) => (
        <BookMenuCard key={book.id} book={book} pageIndex={pageIndex} />
      ))}
      comments={
        <Comments
          entity="book"
          entityId={data.id}
          pathRevalidate={`/media-experience/books/${pageIndex}/${data.id}`}
        />
      }
    />
  );
}

// ==================================================================================
// مكون محتوى الكتاب
// ==================================================================================
function BookContent({ book }: { book: Book }) {
  return (
    <div className="flex-1 space-y-5">
      <h1 className="text-2xl font-bold text-primary md:text-3xl">{book.title}</h1>
      <div className="h-px min-w-max bg-muted/30"></div>
      <div className="flex gap-3">
        {/* غلاف الكتاب */}
        <div className="relative aspect-[1/1.5] w-28 shrink-0 overflow-clip rounded-md border border-muted/0 bg-muted/30 sm:w-40">
          <Image
            sizes="(max-width: 768px) 30vw, 7vw"
            src={book.coverImage}
            className="h-auto w-full object-cover"
            alt={book.title}
            fill
          />
        </div>
        {/* التفاصيل */}
        <div className="flex flex-col space-y-2 text-sm sm:text-base">
          <span>الصفحات : {book.pagesCount}</span>
          <span>
            الســـعر : <BadgePrice price={book.price} />
          </span>
          <span>
            تاريــــخ : <FormatTime dateInput={book.createdAt} />
          </span>
          <div className="">
            <Button variant="outline" className="mt-5">
              تنزيل <Download />
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-5">
        <div className="flex w-full flex-nowrap items-center gap-4">
          <h2 id="lectures" className="scroll-mt-20 text-primary">
            نبذة عن الكتاب
          </h2>
          <hr className="mt-1.5 flex-1 border-primary" />
        </div>
        <p>{book.summary}</p>
      </div>
    </div>
  );
}

// ==================================================================================
// مكون بطاقة الكتاب في القائمة الكتب ذات الصلة
// ==================================================================================
function BookMenuCard({ book, pageIndex }: { book: Book; pageIndex: number }) {
  return (
    <Link
      href={`/media-experience/books/${pageIndex}/${book.id}#pagination`}
      className="group flex snap-start items-start gap-3 rounded-lg bg-linear-to-r to-65% py-3 hover:from-muted/50"
    >
      <div className="relative aspect-[1/1.5] w-[25%] shrink-0 overflow-clip rounded-md bg-muted">
        <Image
          className="h-auto w-full scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          sizes="(max-width: 768px) 20vw, 7vw"
          src={book.coverImage}
          alt={book.title}
          fill
        />
      </div>
      <div className="w-[64%] space-y-1 rounded-lg">
        <p className="w-full truncate text-sm font-medium">{book.title}</p>
        <p className="line-clamp-2 w-full text-xs text-background/70">{book.summary}</p>
        <p className="flex gap-1 pt-1 text-[10px] text-background/70">
          <CalendarDays className="size-3" /> <FormatTime dateInput={book.createdAt} />
        </p>
      </div>
    </Link>
  );
}
