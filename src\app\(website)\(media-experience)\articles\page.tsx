import { Metadata } from "next"
import Link from "next/link"
import { getArticles } from "@/utils/get-data-from-db"
import { Article } from "@prisma/client"
import { CalendarDays } from "lucide-react"

import FormatTime from "@/components/ui/format-time"
import { Pagination } from "@/components/ui/pagination"

type ArticlesProps = {
  searchParams: Promise<{ page?: string }>
}

// ==================================================================================
// صفحة تعرض كل المقالات
// ==================================================================================
export async function generateMetadata(
  props: ArticlesProps
): Promise<Metadata> {
  // const pageIndex = Number((await props.searchParams).page)

  // العلامات الوصفية للترقيم الصفحي
  // const isPrevious = pageIndex > 1
  // const isNext = totalPages > pageIndex
  // const next = isNext
  //   ? `/articles?page=${pageIndex + 1}`
  //   : undefined
  // const previous = isPrevious
  //   ? `/articles?page=${pageIndex - 1}`
  //   : undefined

  return {
    title: `مقالات د. ناهد باشطح`,
    description:
      "مقالات الدكتورة ناهد باشطح في مجال العلاج الشعوري وتقنيات العلاج الشعوري",
    keywords: "مقالات, د. ناهد باشطح, العلاج الشعوري, تقنيات العلاج الشعوري",
    // pagination: { previous, next },
  }
}

export default async function ArticlesPage(props: ArticlesProps) {
  const pageIndex = Number((await props.searchParams).page) || 1 // الصفحة الافتراضية هي الصفحة الاولى اي 1 اذا لم يتم تحديد الصفحة في الروابط الديناميكية في الصفحة الرئيسية للموقع

  const { data, pagination } = await getArticles({ pageIndex })

  if (!data || !data.length) return <div>لم يتم إضافة اي محتويات</div>
  
  return (
    <>
      {data?.map((article) => (
        <ArticleCard key={article.id} article={article} />
      ))}
      <Pagination url="/media-experience/articles" {...pagination} />
    </>
  )
}

// ==================================================================================
// مكون بطاقة المقالة
// يتم استخدامها في هذه الصفحة وفي صفحة المقالة
// ==================================================================================
function ArticleCard({ article }: { article: Article }) {
  return (
    <div className="text-secondary w-full space-y-5 overflow-clip rounded-md bg-white shadow-[0_0_15px_-2px_#00000040]">
      <div className="space-y-2 px-4 pt-3 md:space-y-3 md:pt-5">
        <Link
          href={`/articles/${article.id}#pagination`}
          className="text-primary hover:text-primary/90 text-lg font-bold underline underline-offset-8"
        >
          {article.title}
        </Link>
        <p className="mt-3 line-clamp-3 min-h-[60px] text-sm">
          {article.seoDescription}
        </p>
      </div>
      <div className="bg-muted/10 w-full rounded-sm px-4 py-2">
        <span className="flex flex-nowrap items-center gap-2 text-xs text-nowrap">
          <CalendarDays className="size-4" />{" "}
          <FormatTime dateInput={article.createdAt} />
        </span>
      </div>
    </div>
  )
}
