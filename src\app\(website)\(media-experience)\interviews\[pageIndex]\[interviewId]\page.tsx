import Comments from "@/components/ui/comments/comments";
import PageDivider from "@/components/ui/page-divider";
import FormatTime from "@/components/ui/format-time";
import Image from "next/image";
import Link from "next/link";

import { getInterviews } from "@/utils/get-data-from-db";
import { CalendarDays } from "lucide-react";
import { siteName } from "@/utils/siteConfig";
import { notFound } from "next/navigation";
import { Interview } from "@prisma/client";
import { Metadata } from "next";

type InterviewPageProps = {
  params: Promise<{ interviewId: string; pageIndex: string }>;
};

const genarateData = async (props: InterviewPageProps) => {
  const params = await props.params;
  const interviewId = params.interviewId;
  const pageIndex = Number(params.pageIndex);

  const { data: response } = await getInterviews({ pageIndex });
  const data = response?.find((interview) => interview.id === interviewId);
  const related = response?.filter((interview) => interview.id !== interviewId);
  if (!data) return notFound();

  return { data, related, pageIndex };
};

export async function generateMetadata(props: InterviewPageProps): Promise<Metadata> {
  const { data, pageIndex } = await genarateData(props);

  return {
    title: data.title,
    description: data.seoDescription,
    keywords: data.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar_AR",
      siteName: siteName,
      images: data.thumbnail,
      description: data.seoDescription,
      title: `${data.title} | د. ناهد باشطح`,
      url: `/media-experience/interviews/${pageIndex}/${data.id}`,
    },
  };
}

// ==================================================================================
// صفحة المقابلة
// ==================================================================================
export default async function InterviewPage(props: InterviewPageProps) {
  const { data, related, pageIndex } = await genarateData(props);

  return (
    <PageDivider
      pageContents={
        <iframe
          className="aspect-video h-auto w-full rounded-lg bg-muted"
          src={data.videoUrl}
          title={data.title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      }
      titleMenu="مقابلات ذات صلة"
      menuItems={related?.map((interview) => (
        <InterviewMenuCard
          key={interview.id}
          interview={interview}
          pageIndex={pageIndex}
        />
      ))}
      comments={
        <Comments
          entity="interview"
          entityId={data.id}
          pathRevalidate={`/media-experience/interviews/${pageIndex}/${data.id}`}
        />
      }
    />
  );
}

// ==================================================================================
// مكون بطاقة المقابلة في قائمة المحتوى ذات الصلة
// ==================================================================================
function InterviewMenuCard({
  interview,
  pageIndex,
}: {
  interview: Interview;
  pageIndex: number;
}) {
  return (
    <Link
      href={`/media-experience/interviews/${pageIndex}/${interview.id}#pagination`}
      className="group flex snap-start items-start gap-3 rounded-lg bg-linear-to-r to-65% py-3 hover:from-muted/50"
    >
      <div className="relative aspect-video w-[30%] shrink-0 overflow-clip rounded-md bg-muted/80">
        <Image
          className="h-auto w-full scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          sizes="(max-width: 768px) 80vw, 10vw"
          src={interview.thumbnail}
          alt={interview.title}
          fill
        />
      </div>
      <div className="w-[59%] space-y-1 rounded-lg">
        <p className="w-full truncate text-sm font-medium">{interview.title}</p>
        <p className="line-clamp-2 w-full text-xs text-background/70">
          {interview.description}
        </p>
        <p className="flex gap-1 pt-1 text-[10px] text-background/70">
          <CalendarDays className="size-3" />{" "}
          <FormatTime dateInput={interview.createdAt} />
        </p>
      </div>
    </Link>
  );
}
