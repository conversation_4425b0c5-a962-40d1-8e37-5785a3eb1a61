// import Image from "next/image";
// import videoPlayr from "@/../public/home-files/video-playr.png";

import { Suspense } from "react"
import { getSiteSettings } from "@/utils/get-data-from-db"

import { DividerRedBottom, DividerRedTop } from "../ui/dividers"
import { Skeleton } from "../ui/skeleton"
import { DividerShadow_X, DividerShadow_Y, QuoteMyStory } from "../ui/svg/icons"
import MyStoryVideoSharing from "./video-sharing"

export default function MyStory() {
  return (
    <div className="bg-secondary relative flex w-full flex-col overflow-clip px-3 py-24 lg:flex-row-reverse lg:items-center lg:justify-center lg:px-6">
      <DividerRedTop />

      {/* الدائرة */}
      <div className="bg-primary-gradient-y absolute -right-32 -bottom-5 size-72 rounded-full opacity-50 blur-xs lg:-top-8 lg:bottom-auto lg:size-96"></div>

      {/* الإقتباس */}
      <div className="relative flex items-center justify-center">
        <div className="text-background absolute w-full max-w-80 space-y-3 px-8 text-center sm:max-w-sm sm:px-4 lg:px-6">
          <p className="text-lg font-bold drop-shadow-lg">
            قصتي مع العلاج الشعوري
          </p>
          <p className="pb-2 text-sm font-light">
            “بخبرة تمتد لأكثر من ثلاثة عقود في الإعلام، انتقلتُ بشغف إلى مجال
            العلاج الشعوري لمساعدة الأفراد على تحسين جودة حياتهم.”
          </p>
        </div>
        <QuoteMyStory className="" />
      </div>

      {/* الضل الفاصل */}
      <DividerShadow_X className="mx-auto w-full scale-x-125 scale-y-150 mix-blend-overlay lg:hidden" />
      <DividerShadow_Y className="hidden h-[410px] w-auto scale-y-150 opacity-25 mix-blend-overlay lg:block" />

      {/* الفيديو */}
      <div>
        <div className="bg-background/[0.01] mt-6 rounded-xl p-3 shadow-[0_25px_20px_#00000040] backdrop-blur-2xl lg:max-w-(--breakpoint-sm) lg:shadow-[60px_0_20px_#00000040]">
          <div className="aspect-video">
            <Suspense
              fallback={
                <Skeleton className="h-full w-full animate-pulse rounded-xl"></Skeleton>
              }
            >
              {/* <MyStoryVideo /> */}
            </Suspense>
          </div>
          <MyStoryVideoSharing />
        </div>
      </div>
      <DividerRedBottom />
    </div>
  )
}

async function MyStoryVideo() {
  const siteSettings = await getSiteSettings()

  return (
    <video
      id="myStoryVideo"
      className="max-h-full scroll-mt-32 max-w-full rounded-xl border-0 drop-shadow-[0_0px_6px_#00000099]"
      width="636"
      height="392"
      controls
      poster={siteSettings.myStoryVideoThumbnailUrl || undefined}
      preload="none"
      autoPlay={false}
      src={siteSettings.myStoryVideoUrl}
    >
      <source src={siteSettings.myStoryVideoUrl} type="video/mp4" />
      لا يدعم متصفحك تشغيل الفيديو.
    </video>
  )
}
