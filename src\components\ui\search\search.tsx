"use client"

import * as React from "react"
import Link from "next/link"
import { FileTextIcon, SearchIcon, Text } from "lucide-react"

import { getSearchQuery, GetSearchQueryResult } from "@/lib/search-query"
import { cn } from "@/lib/utils"

import { Button } from "../button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../dialog"
import { Input } from "../input"

export default function Search() {
  const [open, setOpen] = React.useState(false)
  const [isPending, startTransition] = React.useTransition()
  const [searchQuery, setSearchQuery] = React.useState<GetSearchQueryResult>({
    articles: [],
    blogPosts: [],
    books: [],
    courses: [],
    interviews: [],
  })

  const articles = searchQuery.articles
  const blogPosts = searchQuery.blogPosts
  const books = searchQuery.books
  const courses = searchQuery.courses
  const interviews = searchQuery.interviews

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    startTransition(async () => {
      const result = await getSearchQuery(value)
      startTransition(() => setSearchQuery(result))
    })
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className="h-8 w-8 ml-3">
          <SearchIcon />
        </Button>
      </DialogTrigger>
      <DialogContent className={cn("overflow-clip p-0")}>
        <div>
          <Input
            onChange={onChange}
            className="text-secondary placeholder:text-secondary/70! mt-3 rounded-none! border-0! bg-white pr-6 shadow-none! ring-0! ring-offset-0!"
            placeholder="ابحث عن اي شيئ..."
          />
          <div
            className={cn(
              "border-border/30 max-h-[calc(100vh-200px)] overflow-y-auto border-t px-2 pt-1 pb-6",
              isPending && "opacity-60"
            )}
          >
            {blogPosts && blogPosts.length > 0 && (
              <>
                <Link
                  onClick={() => setOpen(false)}
                  href="#"
                  className="flex w-full items-center gap-x-4 rounded-lg px-2 py-2.5 text-sm font-medium hover:bg-gray-100"
                >
                  <FileTextIcon className="text-primary size-5" /> منشورات
                  المدونة
                </Link>
                <ul className="border-primary/50 mr-4 border-r pr-3">
                  {blogPosts.map((post) => (
                    <li key={post.id} className="my-0.5">
                      <Link
                        onClick={() => setOpen(false)}
                        href={`/blog/${post.id}`}
                        className="flex items-center gap-x-3 rounded-lg py-2.5 pr-3 text-sm hover:bg-gray-100"
                      >
                        <Text className="size-5" />
                        <span className="truncate">{post.title}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </>
            )}
          </div>
        </div>
        <DialogHeader className="sr-only">
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  )
}
